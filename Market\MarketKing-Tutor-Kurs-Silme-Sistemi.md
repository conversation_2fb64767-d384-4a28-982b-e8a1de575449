# MarketKing'de Tutor LMS Kurs Silme Sistemi Entegrasyonu

## Genel Bakış

Bu dokümantasyon, MarketKing eklentisinde Tutor LMS'in orijinal kurs silme sisteminin nasıl entegre edildiğini detaylı olarak açıklamaktadır. Sistem, MarketKing'in kendi SweetAlert tabanlı silme sistemini devre dışı bırakıp, Tutor'ın orijinal modal ve AJAX sistemini kullanmaktadır.

## Sorun Analizi

### Orijinal Durum
- MarketKing: SweetAlert kullanarak `marketkingdeleteproduct` action'ı ile silme
- Tutor LMS: Kendi modal sistemi ve `tutor_delete_dashboard_course` action'ı ile silme
- İki sistem arasında uyumsuzluk ve çakışma

### Hedef
- MarketKing'de kırmızı sil butonuna basınca Tutor'ın orijinal modalının çıkması
- Tutor'ın gü<PERSON>lik kontrollerinin ve silme mantığının kullanılması
- Silme işleminden sonra kurslarım sayfasında kalınması

## Teknik Çözüm

### 1. Backend AJAX Handler Entegrasyonu

**Dosya:** `Market/includes/class-marketking-core.php`

#### 1.1 AJAX Action Hook'u
```php
// Satır 130'da eklendi
add_action('wp_ajax_marketking_delete_dashboard_course', array($this, 'marketking_delete_dashboard_course'));
```

#### 1.2 AJAX Handler Fonksiyonu
```php
// Satır 6478-6544 arası
function marketking_delete_dashboard_course() {
    // Tutor'ın nonce checking sistemini kullan
    if (function_exists('tutor_utils')) {
        tutor_utils()->checking_nonce();
    } else {
        wp_send_json_error(array('message' => esc_html__('Security check failed.', 'marketking-multivendor-marketplace-for-woocommerce')));
    }

    // Tutor'ın Input class'ını kullanarak course_id al
    $course_id = 0;
    if (class_exists('TUTOR\Input')) {
        $course_id = \TUTOR\Input::post('course_id', 0, \TUTOR\Input::TYPE_INT);
    } else {
        $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    }

    // Tutor'ın yetki kontrollerini kullan
    if (function_exists('tutor_utils')) {
        if (!tutor_utils()->can_user_manage('course', $course_id)) {
            wp_send_json_error(array('message' => esc_html__('Access Denied', 'marketking-multivendor-marketplace-for-woocommerce')));
        }
    }

    // Ana eğitmen kontrolü
    if (class_exists('TUTOR\Models\CourseModel')) {
        if (false === \TUTOR\Models\CourseModel::is_main_instructor($course_id)) {
            wp_send_json_error(array('message' => esc_html__('Only main instructor can delete this course', 'marketking-multivendor-marketplace-for-woocommerce')));
        }
    }

    // MarketKing vendor izin kontrolü
    $user_id = get_current_user_id();
    if (!current_user_can('administrator')) {
        $is_vendor = false;
        if (function_exists('marketking')) {
            $is_vendor = marketking()->is_vendor($user_id) || marketking()->is_vendor_team_member($user_id);
        }

        if (!$is_vendor && function_exists('tutor_utils')) {
            $can_delete_course = tutor_utils()->get_option('instructor_can_delete_course');
            if (!$can_delete_course) {
                wp_send_json_error(array('message' => esc_html__('You do not have permission to delete courses', 'marketking-multivendor-marketplace-for-woocommerce')));
            }
        }
    }

    // Tutor'ın CourseModel ile kursu sil
    if (class_exists('TUTOR\Models\CourseModel')) {
        $delete_result = \TUTOR\Models\CourseModel::delete_course($course_id);
        
        if ($delete_result) {
            wp_send_json_success(esc_html__('Course has been deleted', 'marketking-multivendor-marketplace-for-woocommerce'));
        } else {
            wp_send_json_error(array('message' => esc_html__('Course delete failed', 'marketking-multivendor-marketplace-for-woocommerce')));
        }
    } else {
        wp_send_json_error(array('message' => esc_html__('Tutor CourseModel not available', 'marketking-multivendor-marketplace-for-woocommerce')));
    }
}
```

### 2. Frontend Modal ve JavaScript Entegrasyonu

**Dosya:** `Market/public/dashboard/my-courses.php`

#### 2.1 Tutor Modal HTML Yapısı
```html
<!-- Satır 630-663 arası eklendi -->
<div id="marketking_course_delete_modal" class="tutor-modal">
    <div class="tutor-modal-overlay"></div>
    <div class="tutor-modal-window">
        <div class="tutor-modal-content tutor-modal-content-white">
            <button class="tutor-iconic-btn tutor-modal-close-o" data-tutor-modal-close>
                <span class="tutor-icon-times" area-hidden="true"></span>
            </button>

            <div class="tutor-modal-body tutor-text-center">
                <div class="tutor-mt-48">
                    <img class="tutor-d-inline-block" src="<?php echo esc_attr(tutor()->url); ?>assets/images/icon-trash.svg" />
                </div>

                <div class="tutor-fs-3 tutor-fw-medium tutor-color-black tutor-mb-12">
                    <?php esc_html_e('Bu Kursu Silinsin mi?', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                </div>
                <div class="tutor-fs-6 tutor-color-muted">
                    <?php esc_html_e('Bu kursu siteden kalıcı olarak silmek istediğinizden emin misiniz? Lütfen seçiminizi onaylayın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                </div>

                <div class="tutor-d-flex tutor-justify-center tutor-my-48">
                    <button data-tutor-modal-close class="tutor-btn tutor-btn-outline-primary">
                        <?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                    <button id="marketking_confirm_delete_course" class="tutor-btn tutor-btn-primary tutor-list-ajax-action tutor-ml-20" 
                            data-request_data='{"course_id":"","action":"marketking_delete_dashboard_course","redirect_to":"<?php echo esc_url(tutor_utils()->get_current_url()); ?>"}'
                            data-delete_element_id="">
                        <?php esc_html_e('Evet, Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### 2.2 CSS Yükleme
```php
// Satır 665-671 arası eklendi
<?php
// Tutor'ın modal stillerinin yüklendiğinden emin ol
if (function_exists('tutor') && !wp_style_is('tutor-frontend', 'enqueued')) {
    wp_enqueue_style('tutor-frontend', tutor()->url . 'assets/css/tutor-front.min.css', array(), TUTOR_VERSION);
}
?>
```

#### 2.3 Sil Butonu Güncelleme
```html
<!-- Satır 422-424, orijinal data-tutor-modal-target yerine marketking_delete_button class'ı -->
<a href="#" class="btn btn-danger btn-sm marketking_delete_button" value="<?php echo esc_attr($post->ID); ?>" title="<?php esc_attr_e('Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
    <em class="icon ni ni-trash"></em>
</a>
```

#### 2.4 JavaScript Event Handler'ları
```javascript
// Satır 686: MarketKing'in orijinal SweetAlert handler'ını devre dışı bırak
$('body').off('click', '.marketking_delete_button');

// Satır 689-712: Sil butonuna tıklandığında Tutor modalını aç
$(document).on('click', '.marketking_delete_button', function(e) {
    e.preventDefault();
    
    var courseId = $(this).attr('value');
    var courseElement = $(this).closest('.tutor-card, .tutor-mycourse-' + courseId);
    var deleteElementId = courseElement.attr('id') || 'tutor-dashboard-my-course-' + courseId;
    
    // Modal verilerini güncelle
    var modal = $('#marketking_course_delete_modal');
    var confirmButton = modal.find('#marketking_confirm_delete_course');
    
    var requestData = {
        course_id: courseId,
        action: 'marketking_delete_dashboard_course',
        redirect_to: window.location.href.split('?')[0] // Query parametrelerini kaldır
    };
    
    confirmButton.attr('data-request_data', JSON.stringify(requestData));
    confirmButton.attr('data-delete_element_id', deleteElementId);
    
    // Tutor'ın modal sistemini kullanarak modalı aç
    modal.addClass('tutor-is-active');
    $('body').addClass('tutor-modal-open');
});

// Satır 715-808: Tutor'ın orijinal AJAX handler'ı (uyarlanmış)
$(document).on('click', '.tutor-list-ajax-action', function(e) {
    e.preventDefault();

    var $button = $(this);
    var requestData = $button.data('request_data');
    var deleteElementId = $button.data('delete_element_id');

    // Request data'yı parse et
    if (typeof requestData === 'string') {
        try {
            requestData = JSON.parse(requestData);
        } catch (e) {
            console.error('Failed to parse request data:', e);
            return;
        }
    }

    // Sadece kurs silme işlemlerini handle et
    if (requestData.action === 'marketking_delete_dashboard_course') {
        var originalText = $button.text();
        $button.prop('disabled', true).text('Siliniyor...');

        // AJAX verilerini Tutor'ın formatında hazırla
        var ajaxData = {
            action: requestData.action,
            course_id: requestData.course_id
        };

        // Tutor nonce'ını ekle
        if (typeof _tutorobject !== 'undefined' && _tutorobject.nonce_key) {
            ajaxData[_tutorobject.nonce_key] = _tutorobject[_tutorobject.nonce_key];
        }

        // AJAX isteği gönder
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: ajaxData,
            success: function(response) {
                if (response.success) {
                    // Modalı kapat
                    $button.closest('.tutor-modal').hide();
                    $('body').removeClass('tutor-modal-open');

                    // Kurs elementini kaldır
                    if (deleteElementId) {
                        $('#' + deleteElementId).fadeOut(300, function() {
                            $(this).remove();
                        });
                    }

                    // Başarı mesajı göster
                    if (response.data && response.data.message) {
                        alert(response.data.message);
                    } else if (typeof response === 'string') {
                        alert(response);
                    } else {
                        alert('Kurs başarıyla silindi!');
                    }

                    // Kurslarım sayfasında kal
                    setTimeout(function() {
                        var coursesPageUrl = window.location.href.split('?')[0];
                        window.location.href = coursesPageUrl;
                    }, 1000);

                } else {
                    var errorMessage = 'Bilinmeyen hata';
                    if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (typeof response === 'string') {
                        errorMessage = response;
                    }
                    alert('Hata: ' + errorMessage);
                    $button.prop('disabled', false).text(originalText);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error, xhr.responseText);
                alert('Sunucu hatası: ' + error);
                $button.prop('disabled', false).text(originalText);
            }
        });
    }
});
```

### 3. Kurs Kartı ID Yapısı

**Dosya:** `Market/public/dashboard/my-courses.php`

```html
<!-- Satır 392: Tutor'ın beklediği ID formatı -->
<div id="tutor-dashboard-my-course-<?php echo esc_attr($post->ID); ?>" class="card card-bordered h-100 course-card">
```

## Sistem Akışı

### 1. Kullanıcı Etkileşimi
1. Kullanıcı kurs kartının üzerine hover yapar
2. Kırmızı sil butonuna tıklar (`.marketking_delete_button`)

### 2. Modal Açılması
1. JavaScript event handler tetiklenir
2. Kurs ID'si alınır (`value` attribute'undan)
3. Modal'daki confirm butonunun `data-request_data` attribute'u güncellenir
4. Tutor'ın modal sistemi kullanılarak modal açılır

### 3. Silme Onayı
1. Kullanıcı "Evet, Sil" butonuna tıklar
2. Tutor'ın `.tutor-list-ajax-action` handler'ı tetiklenir
3. AJAX verisi hazırlanır (Tutor nonce ile)

### 4. Backend İşleme
1. `marketking_delete_dashboard_course` action'ı çağrılır
2. Tutor'ın güvenlik kontrolleri yapılır:
   - Nonce kontrolü
   - Kullanıcı yetki kontrolü
   - Ana eğitmen kontrolü
   - MarketKing vendor kontrolü
3. Tutor'ın `CourseModel::delete_course()` metodu ile kurs silinir

### 5. Frontend Güncelleme
1. Başarılı silme durumunda:
   - Modal kapanır
   - Kurs kartı DOM'dan kaldırılır
   - Başarı mesajı gösterilir
   - Sayfa yenilenir (kurslarım sayfasında kalır)

## Güvenlik Özellikleri

### 1. Tutor'ın Güvenlik Kontrolleri
- **Nonce Kontrolü**: `tutor_utils()->checking_nonce()`
- **Yetki Kontrolü**: `tutor_utils()->can_user_manage('course', $course_id)`
- **Ana Eğitmen Kontrolü**: `CourseModel::is_main_instructor($course_id)`

### 2. MarketKing'in Ek Kontrolleri
- **Vendor Kontrolü**: `marketking()->is_vendor()` veya `marketking()->is_vendor_team_member()`
- **Eğitmen Silme İzni**: `tutor_utils()->get_option('instructor_can_delete_course')`

### 3. Input Sanitization
- **Tutor Input Class**: `\TUTOR\Input::post('course_id', 0, \TUTOR\Input::TYPE_INT)`
- **WordPress Sanitization**: `intval()`, `esc_attr()`, `esc_html()`

## Avantajlar

### 1. Tutarlılık
- Tutor'ın orijinal UI/UX deneyimi korunur
- Aynı güvenlik standartları uygulanır
- Tutarlı hata mesajları ve geri bildirimler

### 2. Güvenlik
- Tutor'ın kanıtlanmış güvenlik kontrolleri kullanılır
- Çift katmanlı yetki kontrolü (Tutor + MarketKing)
- Proper nonce ve CSRF koruması

### 3. Bakım Kolaylığı
- Tutor güncellemelerinden etkilenmez
- MarketKing'in mevcut yapısını bozmaz
- Modüler ve genişletilebilir yapı

## Sorun Giderme

### 1. Modal Açılmıyor
- Tutor CSS'inin yüklendiğini kontrol edin
- Browser console'da JavaScript hatalarını kontrol edin
- `_tutorobject`'in tanımlı olduğunu kontrol edin

### 2. AJAX Hatası
- Nonce değerlerini kontrol edin
- PHP error log'larını kontrol edin
- Network tab'da AJAX request/response'u inceleyin

### 3. Yetki Hatası
- Kullanıcının vendor olduğunu kontrol edin
- Tutor'da eğitmen silme izninin açık olduğunu kontrol edin
- Kullanıcının kursun ana eğitmeni olduğunu kontrol edin

## Sonuç

Bu entegrasyon, MarketKing ve Tutor LMS arasında seamless bir kurs silme deneyimi sağlar. Tutor'ın güvenlik standartlarını korurken, MarketKing'in UI'ını kullanır ve her iki sistemin avantajlarını birleştirir.
